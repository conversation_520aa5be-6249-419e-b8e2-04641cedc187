# queryFeeByOriProduct 主要业务流程

## 流程概述
`queryFeeByOriProduct` 方法用于根据原始产品和查询请求，通过漏斗模型匹配机制查找最合适的费用配置项。

## 详细流程图

```mermaid
flowchart TD
    A[开始: queryFeeByOriProduct] --> B[获取原始产品的费用配置列表]
    B --> C{费用配置列表是否为空?}
    C -->|是| D[抛出异常: 费用信息没找到]
    C -->|否| E[调用 getFunnelMatchItem 构建漏斗匹配项]
    
    E --> F[FunnelMatchItem 构建过程]
    F --> G[从 InstInfoQueryRequest 提取参数:<br/>- paymentCcy 支付币种<br/>- mcc 商户类别码<br/>- subMerchantNo 二级商户号<br/>- channelMerchantCode 渠道商户号<br/>- fundingSource 资金源<br/>- transactionCountry 交易国家<br/>- clearingNetwork 清算网络<br/>- feeBearer 费用承担方<br/>- customerType 客户类型<br/>- cardType 卡类型]
    
    G --> H[调用 funnelModelMatchService.matchFee]
    H --> I[漏斗模型匹配流程开始]
    
    I --> J[遍历所有注册的 ContractMatcher]
    J --> K[当前匹配器: PaymentCcyMatcher<br/>支付币种匹配 - 必须匹配]
    K --> L[精准匹配: 币种完全相等]
    L --> M{精准匹配结果是否为空?}
    M -->|否| N[返回精准匹配结果]
    M -->|是| O[模糊匹配: 币种为 * 的配置]
    
    N --> P[下一个匹配器: SubMerchantNoMatcher<br/>二级商户号匹配]
    O --> P
    
    P --> Q{所有费用项的二级商户号都为空?}
    Q -->|是| R[跳过此匹配器]
    Q -->|否| S[精准匹配: 二级商户号完全相等]
    S --> T{精准匹配结果是否为空?}
    T -->|否| U[返回精准匹配结果]
    T -->|是| V[模糊匹配: 二级商户号为空的配置]
    
    R --> W[下一个匹配器: ChannelMerchantCodeMatcher<br/>渠道商户号匹配]
    U --> W
    V --> W
    
    W --> X{所有费用项的渠道商户号都为空?}
    X -->|是| Y[跳过此匹配器]
    X -->|否| Z[精准匹配: 渠道商户号完全相等]
    Z --> AA{精准匹配结果是否为空?}
    AA -->|否| BB[返回精准匹配结果]
    AA -->|是| CC[模糊匹配: 渠道商户号为空的配置]
    
    Y --> DD[下一个匹配器: FundingSourceMatcher<br/>资金源匹配]
    BB --> DD
    CC --> DD
    
    DD --> EE{所有费用项的资金源都为空?}
    EE -->|是| FF[跳过此匹配器]
    EE -->|否| GG[精准匹配: 资金源完全相等]
    GG --> HH{精准匹配结果是否为空?}
    HH -->|否| II[返回精准匹配结果]
    HH -->|是| JJ[模糊匹配: 资金源为空的配置]
    
    FF --> KK[下一个匹配器: CardTypeMatcher<br/>卡类型匹配]
    II --> KK
    JJ --> KK
    
    KK --> LL{所有费用项的卡类型都为空?}
    LL -->|是| MM[跳过此匹配器]
    LL -->|否| NN[精准匹配: 卡类型完全相等]
    NN --> OO{精准匹配结果是否为空?}
    OO -->|否| PP[返回精准匹配结果]
    OO -->|是| QQ[模糊匹配: 卡类型为空的配置]
    
    MM --> RR[继续其他匹配器...]
    PP --> RR
    QQ --> RR
    
    RR --> SS[所有匹配器执行完毕]
    SS --> TT{最终匹配结果是否为空?}
    TT -->|是| UU[抛出异常: 无匹配数据]
    TT -->|否| VV{匹配结果是否唯一?}
    VV -->|否| WW[抛出异常: 匹配到多条数据]
    VV -->|是| XX[返回唯一匹配的费用配置项]
    
    XX --> YY[回到主流程: 验证最终结果]
    YY --> ZZ{最终费用配置项是否为空?}
    ZZ -->|是| AAA[抛出异常: 兜底配置都没找到]
    ZZ -->|否| BBB[返回最终费用配置项]
    
    BBB --> CCC[结束]
    D --> CCC
    UU --> CCC
    WW --> CCC
    AAA --> CCC
```

## 关键组件说明

### 1. FunnelMatchItem
漏斗匹配项，包含所有匹配维度的参数：
- 支付币种 (paymentCcy) - 必须匹配
- MCC商户类别码 (mcc)
- 二级商户号 (subMerchantNo)
- 渠道商户号 (channelMerchantCode)
- 资金源 (fundingSource)
- 交易国家 (transactionCountry)
- 清算网络 (clearingNetwork)
- 费用承担方 (feeBearer)
- 客户类型 (customerType)
- 卡类型 (cardType)

### 2. 漏斗模型匹配器链
按优先级顺序执行的匹配器：
1. **PaymentCcyMatcher** - 支付币种匹配（必须）
2. **SubMerchantNoMatcher** - 二级商户号匹配
3. **ChannelMerchantCodeMatcher** - 渠道商户号匹配
4. **FundingSourceMatcher** - 资金源匹配
5. **CardTypeMatcher** - 卡类型匹配
6. 其他匹配器...

### 3. 匹配策略
每个匹配器都采用三层过滤策略：
1. **忽略过滤** - 如果所有配置项该字段都为空，直接跳过
2. **精准匹配** - 字段值完全相等
3. **模糊匹配** - 配置项该字段为空或通配符

## 业务特点
- **漏斗式逐层过滤**：每个匹配器都会缩小候选范围
- **精准优先**：优先返回精准匹配结果
- **兜底机制**：通过模糊匹配和通配符配置确保有结果
- **唯一性校验**：最终必须匹配到唯一的费用配置项
